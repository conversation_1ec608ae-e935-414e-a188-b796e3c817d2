# Fabric 视频编辑器：全面的基于画布的视频编辑解决方案

Fabric 视频编辑器是一款功能强大的基于 Web 的视频编辑应用程序，它利用 Fabric.js 的功能提供灵活直观的基于画布的编辑体验。该项目结合了 React 前端和 Node.js 后端，提供了全面的视频编辑工具套件，包括时间轴操作、元素管理和动画控制。

该应用程序允许用户通过组合视频、图像、音频和文本等各种媒体元素来创建、编辑和导出视频作品。它具有一套强大的编辑工具，包括位置和变换控制、效果应用和动画功能。

## 仓库结构

该项目主要分为两个目录：`frontend`和`server`。

### 前端

前端使用 React 构建，包含主要的应用程序逻辑和用户界面组件。

- `src/`：包含 React 应用程序的源代码
  - `components/`：各种 UI 元素的 React 组件
  - `editor/`：编辑器特定的组件和逻辑
  - `store/`：使用 MobX 进行状态管理
  - `utils/`：实用函数和辅助工具
  - `types.ts`：TypeScript 类型定义
  - `App.tsx`：主应用程序组件
  - `index.tsx`：React 应用程序的入口点

### 服务器

服务器目录包含后端代码，用于处理视频处理和 FFmpeg 命令生成。

- `src/`：服务器源代码
  - `ffmpeg/`：FFmpeg 命令生成逻辑
  - `index.ts`：主服务器文件

## 使用说明

### 安装

1. 克隆仓库：

   ```
   git clone <仓库URL>
   ```

2. 为前端和服务器安装依赖：

   ```
   cd frontend && npm install
   cd ../server && npm install
   ```

3. 启动开发服务器：
   - 对于前端：
     ```
     cd frontend && npm run dev
     ```
   - 对于后端：
     ```
     cd server && npm run dev
     ```

### 入门指南

1. 在网络浏览器中打开应用程序（通常在`http://localhost:3000`）。
2. 使用左侧边栏将媒体元素（视频、图像、音频）添加到您的作品中。
3. 使用提供的控件在画布上操作元素的位置、缩放和旋转。
4. 使用底部的时间轴调整元素持续时间并添加动画。
5. 使用右侧边栏控件对元素应用效果和样式。
6. 使用顶部菜单中的导出选项导出您的作品。

### 主要功能

- 基于画布的编辑，具有直观的控件
- 用于管理元素持续时间和动画的时间轴
- 支持各种媒体类型（视频、图像、音频、文本）
- 可自定义的动画和效果
- FFmpeg 集成用于视频处理和导出

### 故障排除

- 如果您在元素渲染方面遇到问题，请确保您的浏览器支持所需的视频和音频编解码器。
- 对于性能问题，尝试减少作品中的元素数量或简化动画。
- 检查浏览器控制台中的任何错误消息，这些消息可能指示特定问题。

## 数据流

1. 用户与 React 前端的 UI 组件交互。
2. 操作触发 MobX 存储（`Store.ts`）中的状态更新。
3. `ElementManager`和`AnimationManager`分别处理元素和动画逻辑。
4. 画布更新通过 Fabric.js 管理，它渲染作品。
5. 对于视频导出，前端将画布状态发送到后端。
6. 后端使用`FFmpegCommandGenerator`生成 FFmpeg 命令。
7. FFmpeg 根据生成的命令处理视频。
8. 处理后的视频发送回前端以供下载。

```
[用户输入] -> [React组件] -> [MobX存储] -> [管理器] -> [Fabric.js画布]
                                       -> [后端API] -> [FFmpeg] -> [处理后的视频]
```

## 视频导出功能

### 支持的导出格式

- **视频格式**: MP4（默认）、AVI、MOV、WebM
- **音频格式**: MP3（纯音频导出）
- **质量选项**: 高质量、中等质量、低质量
- **编码器**: H.264（视频）、MP3（音频）

### 导出配置

系统会根据选择的格式自动配置最佳的编码参数：

- **视频编码**: 使用 libx264 编码器，支持硬件加速
- **音频编码**: 使用 MP3 编码器进行音频导出
- **帧率**: 默认 30fps，音频导出时自动设置为 0
- **质量**: 支持高、中、低三种质量预设

## 基础设施

Fabric 视频编辑器应用程序采用客户端-服务器架构设计，利用现代 Web 技术提供响应迅速且功能强大的视频编辑体验。以下是基础设施的详细分解：

### 前端基础设施

1. **框架**：React.js

   - 提供基于组件的架构来构建用户界面
   - 利用 TypeScript 增强类型安全性和开发者体验

2. **状态管理**：MobX

   - 管理应用程序的状态，包括编辑器元素、动画和 UI 控件
   - 启用响应式编程模式以实现高效更新

3. **画布操作**：Fabric.js

   - 在 HTML5 画布上提供核心编辑功能
   - 处理对象操作、渲染和事件管理

4. **动画**：Anime.js

   - 管理编辑器元素的复杂动画

5. **UI 组件**：Material-UI (MUI)

   - 提供预构建的、可自定义的 React 组件，以实现一致的外观和感觉

6. **开发工具**：
   - Webpack 用于打包和资产管理
   - Babel 用于 JavaScript 转译
   - ESLint 用于代码质量和风格强制执行

### 后端基础设施

1. **服务器**：Node.js 与 Express.js

   - 处理来自前端的 API 请求
   - 管理视频处理任务

2. **视频处理**：FFmpeg

   - 需要安装在服务器上
   - 根据前端的编辑指令处理视频文件

3. **API 端点**：
   - `/api/generateVideo`：接受画布状态并生成视频
   - `/api/progress/:taskId`：提供视频生成任务的进度更新

### 数据库

当前实现不包括数据库。用户项目和资产通过内存和浏览器本地存储进行管理。对于生产环境，考虑实现：

- 数据库系统（例如 PostgreSQL、MongoDB）用于持久存储用户项目和资产
- 用户认证和授权，以安全访问项目

### 部署考虑因素

1. **前端部署**：

   - 可以部署到静态文件托管服务（例如 Netlify、Vercel、带有 CloudFront 的 AWS S3）
   - 需要适当配置路由以支持单页应用程序行为

2. **后端部署**：

   - 需要 Node.js 运行时环境
   - 可以部署到云平台，如 Heroku、AWS Elastic Beanstalk 或 Google Cloud Run
   - 在部署环境中需要安装 FFmpeg

3. **扩展**：
   - 考虑容器化（例如 Docker）以在各环境中实现一致部署
   - 为后端实现负载均衡，以处理多个并发视频处理任务
   - 使用作业队列系统（例如 Redis、RabbitMQ）管理视频处理任务

### 第三方服务和依赖项

- Fabric.js 用于画布操作
- Anime.js 用于动画
- Material-UI 用于 UI 组件
- FFmpeg 用于视频处理（服务器端）

### 性能和可扩展性考虑因素

1. **前端性能**：

   - 实现代码分割和懒加载，以改善初始加载时间
   - 优化大型作品的画布渲染
   - 考虑使用 Web Workers 进行重计算，以防止 UI 阻塞

2. **后端可扩展性**：

   - 实现微服务架构，将视频处理与其他服务器功能分离
   - 使用分布式任务队列在多个工作实例之间管理视频处理作业
   - 实现缓存机制，减少类似视频作品的冗余处理

3. **资产管理**：

   - 利用内容分发网络（CDN）高效传递静态资产和处理后的视频
   - 为用户上传的媒体文件实现高效的存储和检索机制

4. **监控和日志记录**：
   - 为前端和后端实现应用程序性能监控（APM）工具
   - 设置日志记录和错误跟踪服务，以快速识别和解决问题

通过考虑这些基础设施组件和优化，Fabric 视频编辑器可以扩展以处理不断增长的用户群和更复杂的视频编辑任务，同时保持性能和可靠性。

## Jamendo API 集成

本项目已集成 Jamendo API 用于在线音频库。按照以下步骤配置:

1. 在 [Jamendo 开发者门户](https://devportal.jamendo.com/) 注册账号并创建应用程序
2. 获取 API 密钥
3. 在根目录创建`.env`文件，添加以下内容:
   ```
   REACT_APP_JAMENDO_API_KEY=你的API密钥
   ```
4. 重新启动应用

## 可用的音频源

- 本地音频库
- Jamendo 免费音乐库
  - 支持搜索功能
  - 提供热门曲目浏览
  - **已解决跨域问题**：通过后端代理服务自动处理 CORS 限制

---

## 📚 详细文档

### 用户文档

- [用户使用指南](./docs/USER_GUIDE.md) - 完整的用户操作手册
- [故障排除](./docs/TROUBLESHOOTING.md) - 常见问题解决方案

### 开发者文档

- [API 文档](./docs/API.md) - 完整的后端 API 接口文档
- [前端开发指南](./docs/FRONTEND_GUIDE.md) - 前端开发和集成指南
- [部署指南](./docs/DEPLOYMENT.md) - 生产环境部署说明
- [贡献指南](./docs/CONTRIBUTING.md) - 如何参与项目开发

### 技术文档

- [架构文档](./server/ARCHITECTURE.md) - 系统架构设计
- [动画同步实现](./ANIMATION_SYNC_IMPLEMENTATION_SUMMARY.md) - 前后端动画同步机制
- [字幕位置修复](./SUBTITLE_POSITION_FIX.md) - 字幕定位优化方案
- [更新日志](./CHANGELOG.md) - 版本更新记录
