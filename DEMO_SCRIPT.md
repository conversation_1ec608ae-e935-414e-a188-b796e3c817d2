# 时间线自适应布局功能演示脚本

## 演示步骤

### 1. 打开编辑器

- 访问 http://localhost:3001
- 进入视频编辑器界面

### 2. 观察新的布局特性

- 时间线组件现在填充所有可用的垂直空间
- 水平滚动条位于页面最下方
- 时间线具有最小高度限制（默认 250px）

### 3. 悬停测试

- 将鼠标移动到时间线面板顶部边框
- 观察鼠标光标变为垂直调整样式（↕️）
- 查看显示的提示文字："拖拽调整时间线最小高度"

### 4. 最小高度调整演示

#### 增加最小高度：

1. 在时间线顶部边框按下鼠标左键
2. 向下拖拽鼠标
3. 观察：
   - 时间线最小高度实时增加
   - 右上角显示当前最小高度数值
   - 顶部边框显示蓝色高亮
   - 时间线仍然填充所有可用空间

#### 减少最小高度：

1. 在时间线顶部边框按下鼠标左键
2. 向上拖拽鼠标
3. 观察相同的视觉反馈

### 5. 边界测试

- 尝试拖拽到最小高度（150px）
- 尝试拖拽到最大高度（600px）
- 验证超出范围时的限制效果

### 6. 布局测试

1. 调整浏览器窗口大小
2. 观察时间线组件如何填充可用空间
3. 验证水平滚动条始终在页面底部

### 7. 持久化测试

1. 调整时间线最小高度到一个特定值（如 400px）
2. 刷新页面
3. 验证最小高度设置是否保持

### 8. 交互测试

- 在拖拽过程中验证时间线其他功能是否被正确禁用
- 释放鼠标后验证所有功能恢复正常
- 测试时间线内容的垂直和水平滚动

## 预期效果

### 视觉反馈

- ✅ 悬停时显示操作提示
- ✅ 拖拽时顶部边框高亮
- ✅ 实时显示高度数值
- ✅ 鼠标光标样式变化

### 功能表现

- ✅ 高度调整平滑流畅
- ✅ 范围限制正确生效
- ✅ 画布容器响应式调整
- ✅ 设置持久化保存

### 用户体验

- ✅ 操作直观易懂
- ✅ 视觉反馈清晰
- ✅ 性能流畅无卡顿
- ✅ 不影响其他功能

## 可能的问题排查

### 如果拖拽不响应：

1. 检查鼠标是否在正确的拖拽区域
2. 确认浏览器支持鼠标事件
3. 查看控制台是否有 JavaScript 错误

### 如果高度不保存：

1. 检查浏览器 localStorage 是否启用
2. 确认没有隐私模式限制
3. 查看开发者工具中的 localStorage 内容

### 如果画布不响应：

1. 检查 CanvasContainer 组件的高度计算
2. 确认 Store 状态更新正常
3. 验证 CSS 样式是否正确应用

## 技术验证点

- [ ] Store.timelineHeight 状态正确更新
- [ ] localStorage 正确读写
- [ ] 鼠标事件正确绑定和解绑
- [ ] CSS 样式正确应用
- [ ] 组件重渲染性能良好
