# 更新日志

本文档记录了 Fabric 视频编辑器的所有重要更改。

## [未发布] - 2025-01-29

### 改进

- **代码重构**: 重构了 `ProjectManager.ts` 中的视频导出功能
  - 提取 `_getOutputFormatConfig` 方法，提高代码可维护性
  - 统一使用常量配置，避免硬编码值
  - 改进了音频和视频格式的处理逻辑

### 技术改进

- 优化了输出格式配置的代码结构
- 提高了代码的可读性和可维护性
- 修复了未使用常量的 TypeScript 警告

### 文档更新

- 更新了 README.md，添加了视频导出功能说明
- 更新了 API 文档，详细说明了输出格式配置
- 更新了前端开发指南，添加了代码重构最佳实践

## [1.0.0] - 2024-12-01

### 新功能

- 基于 Fabric.js 的画布编辑器
- 多媒体元素支持（视频、图像、音频、文本、形状）
- 时间轴编辑功能
- 动画系统
- 字幕和标题支持
- FFmpeg 视频导出
- Jamendo 音乐库集成
- 项目保存和加载

### 技术特性

- React + TypeScript 前端
- MobX 状态管理
- Node.js + Express 后端
- AWS 服务集成（S3、Transcribe、Bedrock）
- 响应式设计
- 多语言支持

### 支持的格式

- **视频导出**: MP4, AVI, MOV, WebM
- **音频导出**: MP3
- **导入格式**: 常见的视频、音频、图像格式
