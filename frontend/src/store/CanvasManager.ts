import { fabric } from "fabric";
import { makeAutoObservable } from "mobx";
import { Store } from "./Store";
import { CONSTANTS } from "./constants";

export class CanvasManager {
  private store: Store;

  editMode: "move" | "hand" = "move";
  canvasScale: number = 0.2;
  canvasTranslation: { x: number; y: number } = { x: 0, y: 0 };

  _pan: {
    enable: boolean;
    isDragging: boolean;
    lastPosX: number;
    lastPosY: number;
  };

  constructor(store: Store) {
    this.store = store;
    this._pan = {
      enable: false,
      isDragging: false,
      lastPosX: 0,
      lastPosY: 0,
    };
    makeAutoObservable(this);
  }

  get canvas(): fabric.Canvas | null {
    return this.store.canvas;
  }

  /**
   * 设置编辑模式
   * @param mode 编辑模式：'move'或'hand'
   */
  setEditMode(mode: "move" | "hand") {
    // 如果模式没有变化，不做任何操作
    if (this.editMode === mode) return;

    // 保存旧模式，用于日志记录
    const oldMode = this.editMode;
    console.log(`切换编辑模式: ${oldMode} -> ${mode}`);
    this.editMode = mode;

    if (!this.canvas) return;

    // 如果切换到手工具模式，禁用所有对象的可选择性和可移动性
    if (mode === "hand") {
      this.canvas.discardActiveObject();
      this.store.clearAllSelections();

      // 禁用所有对象的可选择性和可移动性
      this.canvas.getObjects().forEach((obj) => {
        obj.selectable = false;
        obj.evented = false; // 禁止所有事件交互
      });

      this.canvas.requestRenderAll();
    } else {
      // 当切换回移动模式时，启用所有对象的可选择性和可移动性
      this.canvas.getObjects().forEach((obj) => {
        obj.selectable = true;
        obj.evented = true; // 恢复事件交互
      });

      this.canvas.requestRenderAll();
    }
  }

  /**
   * 更新画布缩放值
   * @param scale 新的缩放值
   * @param translation 新的平移值（可选）
   */
  updateCanvasScale(scale: number, translation?: { x: number; y: number }) {
    this.canvasScale = this._clampScale(scale);

    if (translation) {
      this.canvasTranslation = translation;
    }
  }

  private _clampScale(scale: number): number {
    return Math.max(
      CONSTANTS.CANVAS.MIN_SCALE,
      Math.min(CONSTANTS.CANVAS.MAX_SCALE, scale)
    );
  }

  /**
   * 计算基于当前画布状态的缩放比例，考虑时间线高度
   */
  private calculateOptimalCanvasScale() {
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;
    const timelineHeight = this.store.timelineHeight;
    const navbarHeight = 60; // 导航栏高度

    // 可用的画布区域高度
    const availableHeight = windowHeight - timelineHeight - navbarHeight;

    // 计算缩放比例，确保画布不被遮挡
    const scaleX = (windowWidth * 0.8) / this.store.canvasWidth; // 增加水平边距
    const scaleY = (availableHeight * 0.8) / this.store.canvasHeight; // 根据可用高度计算
    const scale = Math.min(scaleX, scaleY, 1.0); // 限制最大缩放为1.0

    // 计算画布居中位置
    const x = windowWidth / 2;
    const y = availableHeight / 2 + navbarHeight;

    return { scale, translation: { x, y } };
  }

  /**
   * 放大画布
   * @param step 缩放步长，默认为0.1
   */
  zoomIn(step: number = CONSTANTS.CANVAS.ZOOM_STEP) {
    // 获取当前最优缩放状态作为基准
    const { scale: optimalScale, translation: optimalTranslation } =
      this.calculateOptimalCanvasScale();

    // 基于当前缩放值进行放大，但使用最新的平移位置
    const newScale = this.canvasScale + step;
    this.updateCanvasScale(newScale, optimalTranslation);

    return {
      scale: this.canvasScale,
      translation: this.canvasTranslation,
    };
  }

  /**
   * 缩小画布
   * @param step 缩放步长，默认为0.1
   */
  zoomOut(step: number = CONSTANTS.CANVAS.ZOOM_STEP) {
    // 获取当前最优缩放状态作为基准
    const { scale: optimalScale, translation: optimalTranslation } =
      this.calculateOptimalCanvasScale();

    // 基于当前缩放值进行缩小，但使用最新的平移位置
    const newScale = this.canvasScale - step;
    this.updateCanvasScale(newScale, optimalTranslation);

    return {
      scale: this.canvasScale,
      translation: this.canvasTranslation,
    };
  }

  /**
   * 重置画布缩放
   */
  resetZoom() {
    // 使用动态计算的最优缩放状态
    const { scale, translation } = this.calculateOptimalCanvasScale();

    this.updateCanvasScale(scale, translation);
    return {
      scale: this.canvasScale,
      translation: this.canvasTranslation,
    };
  }
}
