import { observer } from "mobx-react-lite";
import { Box, Paper, useTheme } from "@mui/material";
import { fabric } from "fabric";
import React, { useEffect, useRef, useState } from "react";
import { MapInteractionCSS } from "react-map-interaction";
import { StoreContext } from "../store";

import initControl, {
  handleMouseOutCorner,
  handleMouseOverCorner,
} from "./components/controller";
import { initAligningGuidelines, initCenteringGuidelines } from "./guide-lines";

export const CanvasContainer = observer(() => {
  const store = React.useContext(StoreContext);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const boxRef = useRef<HTMLDivElement>(null);
  const theme = useTheme();

  // 合并相关状态
  const [selectedObject, setSelectedObject] = useState<fabric.Object | null>(
    null
  );
  const [hasSelectedElement, setHasSelectedElement] = useState(false);

  // 计算画布缩放比例，考虑时间线高度
  const calculateCanvasScale = () => {
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;
    const timelineHeight = store.timelineHeight;
    const navbarHeight = 60; // 导航栏高度

    // 可用的画布区域高度
    const availableHeight = windowHeight - timelineHeight - navbarHeight;

    // 计算缩放比例，确保画布不被遮挡
    const scaleX = (windowWidth * 0.8) / 1920; // 增加水平边距
    const scaleY = (availableHeight * 0.8) / 1080; // 根据可用高度计算
    const scale = Math.min(scaleX, scaleY, 1.0); // 限制最大缩放为1.0

    // 计算画布居中位置
    const x = windowWidth / 2;
    const y = availableHeight / 2 + navbarHeight;

    return { scale, translation: { x, y } };
  };

  // 使用Store中的缩放值，提取初始化逻辑为单独函数
  const initializeCanvasScale = () => {
    const { scale, translation } = calculateCanvasScale();
    store.updateCanvasScale(scale, translation);
    return {
      scale: store.canvasScale,
      translation: store.canvasTranslation,
    };
  };

  const [value, setValue] = useState(initializeCanvasScale);

  // 防抖函数
  const debounce = (func: Function, wait: number) => {
    let timeout: NodeJS.Timeout;
    return (...args: any[]) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(null, args), wait);
    };
  };

  // 防抖的缩放调整函数（完整重新计算缩放和平移）
  const debouncedScaleAdjustment = debounce(() => {
    const { scale, translation } = calculateCanvasScale();
    const newValue = { scale, translation };

    // 使用requestAnimationFrame确保平滑更新
    requestAnimationFrame(() => {
      setValue(newValue);
      store.updateCanvasScale(scale, translation);
    });
  }, 30); // 30ms防抖

  // 防抖的平移调整函数（只调整平移位置，保持当前缩放值）
  const debouncedTranslationAdjustment = debounce(() => {
    const { translation } = calculateCanvasScale();
    // 保持当前的缩放值，只更新平移位置
    const newValue = { scale: store.canvasScale, translation };

    // 使用requestAnimationFrame确保平滑更新
    requestAnimationFrame(() => {
      setValue(newValue);
      store.updateCanvasScale(store.canvasScale, translation);
    });
  }, 30); // 30ms防抖

  // 监听时间线高度变化，只调整画布平移位置（保持用户设置的缩放值）
  useEffect(() => {
    debouncedTranslationAdjustment();
  }, [store.timelineHeight]); // 依赖时间线高度

  // 监听窗口大小变化，重新计算画布缩放
  useEffect(() => {
    const handleResize = () => {
      debouncedScaleAdjustment();
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []); // 移除时间线高度依赖，避免重复监听

  // 优化Canvas初始化
  useEffect(() => {
    if (store?.canvas) return;

    const canvas = new fabric.Canvas("myCanvas", {
      selection: true,
      preserveObjectStacking: true,
      selectionColor: "rgba(59, 130, 246, 0.08)",
      selectionBorderColor: "rgba(59, 130, 246, 0.6)",
      enableRetinaScaling: true,
      fireRightClick: false,
      controlsAboveOverlay: true,
      imageSmoothingEnabled: true,
      width: 1920,
      height: 1080,
    });

    // 设置Canvas属性
    const setCanvasProperties = () => {
      fabric.Object.prototype.cornerSize = 20;
      fabric.Object.prototype.borderColor = "rgba(59, 130, 246, 0.6)";
      fabric.Object.prototype.cornerColor = "white";
      fabric.Object.prototype.cornerStrokeColor = "rgba(59, 130, 246, 0.4)";
      fabric.Object.prototype.borderOpacityWhenMoving = 0.8;
      fabric.Object.prototype.centeredScaling = false;
      fabric.Object.prototype.centeredRotation = true;
      fabric.Object.prototype.transparentCorners = false;
    };

    setCanvasProperties();

    // @ts-ignore
    canvas.store = store;
    store.setCanvas(canvas);

    initControl();
    initAligningGuidelines(canvas);
    initCenteringGuidelines(canvas);

    // 初始化事件监听
    canvas.on("mouse:up", handleMouseUp);
    canvas.on("mouse:over", handleMouseOver);
    canvas.on("mouse:out", handleMouseOut);
    canvas.on("mouse:down", handleMouseDown);
    canvas.on("selection:created", handleSelection);
    canvas.on("selection:updated", handleSelection);
    canvas.on("selection:cleared", handleSelectionCleared);

    if (boxRef.current) {
      canvas.setWidth(boxRef.current.offsetWidth);
      canvas.setHeight(boxRef.current.offsetHeight);
    }

    canvas.requestRenderAll();
    store.loadFromLocalStorage();

    return () => {
      if (store.canvas) {
        canvas.off("mouse:up", handleMouseUp);
        canvas.off("mouse:over", handleMouseOver);
        canvas.off("mouse:out", handleMouseOut);
        canvas.off("mouse:down", handleMouseDown);
        canvas.off("selection:created", handleSelection);
        canvas.off("selection:updated", handleSelection);
        canvas.off("selection:cleared", handleSelectionCleared);
        store.destroy();
      }
    };
  }, [theme]);

  // 简化事件处理函数
  const handleSelection = (e: fabric.IEvent) => {
    const selected = e.selected?.[0];
    setSelectedObject(selected || null);
    setHasSelectedElement(!!selected);

    // 检查是否选中了字幕对象
    if (selected && store.captionManager) {
      const captionTextObject = (store.captionManager as any).captionTextObject;
      if (selected === captionTextObject) {
        // 如果选中的是字幕对象，找到对应的字幕并选中
        const currentCaption = store.captionManager.findCurrentCaption(
          store.currentTimeInMs
        );
        if (currentCaption && !currentCaption.isSelected) {
          store.selectCaption(currentCaption.id);
        }
      } else {
        // 如果选中的是其他对象（非字幕），同步store的选中状态
        const element = store.editorElements.find(
          (el) => el.fabricObject === selected
        );
        if (element && store.selectedElement?.id !== element.id) {
          // 只有当选中的元素与store中的不同时才更新，避免循环调用
          store.selectedElement = element;
          store.captionManager.deselectAllCaptions();
        }
      }
    } else if (!selected) {
      // 如果没有选中任何对象，清除store的所有选中状态
      store.clearAllSelections();
    }
  };

  const handleSelectionCleared = () => {
    setSelectedObject(null);
    setHasSelectedElement(false);
  };

  const handleMouseUp = () => {};

  const handleMouseOver = (e: fabric.IEvent) => {
    const corner = e.target ? (e.target as any).__corner : undefined;
    if (corner) {
      handleMouseOverCorner(corner, e.target);
    }
    store.canvas.renderAll();
  };

  const handleMouseOut = (e: fabric.IEvent) => {
    if (e.target) {
      e.target.set({ borderColor: null });
      handleMouseOutCorner(e.target);
      store.canvas.requestRenderAll();
    }
  };

  const handleMouseDown = (opt: fabric.IEvent) => {
    if (store.editMode === "move") {
      if (!opt.target) {
        // 点击画布空白区域，清除所有选择状态
        store.clearAllSelections();
        setHasSelectedElement(false);
      } else {
        setHasSelectedElement(true);
      }
    } else {
      store.canvas.discardActiveObject();
      store.clearAllSelections();
      setHasSelectedElement(false);
      store.canvas.requestRenderAll();
    }
  };

  // 优化编辑模式变化处理
  useEffect(() => {
    if (!store.canvas) return;

    const isHandMode = store.editMode === "hand";
    store.canvas.selection = !isHandMode;

    if (isHandMode) {
      store.canvas.discardActiveObject();
      store.setSelectedElement(null);
      store.deselectAllCaptions();
      setHasSelectedElement(false);
    }

    // 批量设置对象属性
    store.canvas.getObjects().forEach((obj) => {
      obj.selectable = !isHandMode;
      obj.evented = !isHandMode;
    });

    store.canvas.requestRenderAll();
  }, [store.editMode]);

  // 监听画布缩放变化事件
  useEffect(() => {
    const handleCanvasZoomChange = (event: CustomEvent) => {
      setValue(event.detail);
    };

    window.addEventListener(
      "canvas-zoom-change",
      handleCanvasZoomChange as EventListener
    );
    return () => {
      window.removeEventListener(
        "canvas-zoom-change",
        handleCanvasZoomChange as EventListener
      );
    };
  }, []);

  // 监听字幕选中状态变化，确保canvas上的字幕元素也被选中
  useEffect(() => {
    if (!store.canvas || !store.captionManager) return;

    const selectedCaption = store.getSelectedCaption();
    if (selectedCaption) {
      // 如果有选中的字幕，但canvas上的字幕对象未被选中，则选中它
      const captionTextObject = (store.captionManager as any).captionTextObject;
      if (
        captionTextObject &&
        store.canvas.getActiveObject() !== captionTextObject
      ) {
        store.canvas.setActiveObject(captionTextObject);
        store.canvas.requestRenderAll();
      }
    }
  }, [store.captions]); // 监听字幕数组的变化

  // 监听store中selectedElement的变化，确保canvas的选中状态同步
  useEffect(() => {
    if (!store.canvas) return;

    const currentActiveObject = store.canvas.getActiveObject();

    if (store.selectedElement?.fabricObject) {
      // 如果store中有选中的元素，但canvas中的活动对象不是这个元素，则同步
      if (currentActiveObject !== store.selectedElement.fabricObject) {
        store.canvas.setActiveObject(store.selectedElement.fabricObject);
        store.canvas.requestRenderAll();
      }
    } else if (currentActiveObject && !store.getSelectedCaption()) {
      // 如果store中没有选中的元素，且没有选中的字幕，但canvas中有活动对象，则清除
      store.canvas.discardActiveObject();
      store.canvas.requestRenderAll();
    }
  }, [store.selectedElement]); // 监听selectedElement的变化

  // 优化渲染部分
  return (
    <Paper
      elevation={3}
      sx={{
        width: "100%",
        height: `100%`, // 动态计算高度
        overflow: "hidden",
        position: "relative",
      }}
    >
      <Box
        id="grid-canvas-container"
        ref={boxRef}
        sx={(theme) => ({
          width: "100%",
          height: "100%",
          backgroundImage: `radial-gradient(circle at 15px 15px, ${theme.palette.grey[100]} 2px, transparent 0)`,
          backgroundSize: "35px 35px",
          backgroundPosition: "center center",
        })}
      >
        <MapInteractionCSS
          onChange={(newValue: any) => {
            setValue(newValue);
            store.updateCanvasScale(newValue.scale, newValue.translation);
          }}
          value={value}
          disablePan={store.editMode === "move"}
          disableZoom={store.editMode === "move"}
          defaultScale={store.canvasScale}
          minScale={0.1}
          maxScale={2}
          style={{
            width: "100%",
            height: "100%",
            position: "relative",
          }}
        >
          <Box
            sx={(theme) => ({
              position: "absolute",
              left: "50%",
              top: "50%",
              transform: "translate(-50%, -50%)",
              boxShadow: 3,
              borderRadius: 1,
              overflow: "hidden",
              border: `2px solid ${
                theme.palette.mode === "dark"
                  ? theme.palette.grey[700]
                  : theme.palette.grey[300]
              }`,
            })}
          >
            <canvas id="myCanvas" ref={canvasRef} />
          </Box>
        </MapInteractionCSS>
      </Box>
    </Paper>
  );
});
