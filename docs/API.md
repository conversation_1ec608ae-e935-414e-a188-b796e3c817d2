# Fabric 视频编辑器 API 文档

## 概述

本文档详细描述了 Fabric 视频编辑器后端 API 的所有端点、请求格式、响应格式和错误处理。

**基础 URL**: `http://localhost:8080/api`

## 认证

当前版本不需要认证，但建议在生产环境中实现适当的认证机制。

## 速率限制

- **一般 API**: 每 15 分钟最多 2000 个请求
- **进度查询 API**: 每分钟最多 120 个请求
- **超出限制**: 返回 429 状态码和错误信息

## 通用响应格式

### 成功响应

```json
{
  "data": {},
  "message": "操作成功"
}
```

### 错误响应

```json
{
  "error": "错误描述",
  "details": "详细错误信息",
  "code": "ERROR_CODE"
}
```

## 视频处理 API

### 1. 生成视频

**端点**: `POST /api/generateVideo`

**描述**: 根据画布状态生成视频文件

**请求头**:

```
Content-Type: application/json
```

**请求体**:

```json
{
  "width": 1280,
  "height": 720,
  "backgroundColor": "#111111",
  "elements": [
    {
      "type": "video|image|audio|text|shape",
      "id": "unique_element_id",
      "properties": {
        "elementId": "dom_element_id",
        "src": "media_url",
        "text": "文本内容",
        "fontSize": 24,
        "fontFamily": "Arial",
        "color": "#ffffff"
      },
      "placement": {
        "x": 100,
        "y": 100,
        "width": 200,
        "height": 150,
        "rotation": 0,
        "scaleX": 1,
        "scaleY": 1,
        "flipX": false,
        "flipY": false
      },
      "timeFrame": {
        "start": 0,
        "end": 5000
      },
      "opacity": 1,
      "transition": {
        "in": "fade",
        "out": "none",
        "duration": 1
      },
      "effects": [
        {
          "type": "blur",
          "params": { "amount": 5 }
        }
      ],
      "border": {
        "color": "#ff0000",
        "width": 2,
        "style": "solid"
      }
    }
  ],
  "outputFormat": {
    "codec": "libx264",
    "format": "mp4",
    "quality": "high",
    "frameRate": 30
  }
}
```

**响应**:

```json
{
  "taskId": "1703123456789",
  "queuePosition": 1,
  "message": "任务已开始处理"
}
```

**输出格式配置说明**:

系统会根据选择的格式自动配置最佳的编码参数：

- **视频格式** (mp4, avi, mov, webm):

  - 编码器: libx264
  - 帧率: 30fps (可配置)
  - 质量: high/medium/low

- **音频格式** (mp3):
  - 编码器: mp3
  - 帧率: 0 (音频不需要帧率)
  - 质量: 根据比特率调整

**错误响应**:

- `400 Bad Request`: 请求参数无效
- `413 Payload Too Large`: 请求体过大（超过 50MB）
- `500 Internal Server Error`: 服务器内部错误

### 2. 查询进度

**端点**: `GET /api/progress/:taskId`

**描述**: 查询视频生成任务的进度

**路径参数**:

- `taskId`: 任务 ID

**响应**:

```json
{
  "taskId": "1703123456789",
  "status": "processing",
  "progress": 45.5,
  "stage": "video_processing",
  "currentFrame": 455,
  "totalFrames": 1000,
  "fps": 30,
  "speed": "1.2x",
  "eta": "00:02:30",
  "queuePosition": 0,
  "startedAt": "2023-12-21T10:30:00Z",
  "message": "正在处理视频..."
}
```

**状态说明**:

- `queued`: 任务在队列中等待
- `processing`: 正在处理
- `completed`: 处理完成
- `failed`: 处理失败

### 3. 取消任务

**端点**: `POST /api/cancel/:taskId`

**描述**: 取消正在进行的视频生成任务

**路径参数**:

- `taskId`: 任务 ID

**响应**:

```json
{
  "message": "任务已取消",
  "taskId": "1703123456789"
}
```

### 4. 下载视频

**端点**: `GET /api/download/:taskId`

**描述**: 下载生成的视频文件

**路径参数**:

- `taskId`: 任务 ID

**响应**:

- 成功: 返回视频文件流
- 失败: JSON 错误信息

**响应头**:

```
Content-Type: video/mp4
Content-Disposition: attachment; filename="video-{taskId}.mp4"
```

## 转录服务 API

### 1. 生成字幕

**端点**: `POST /api/transcribe`

**描述**: 从音频或视频 URL 生成 SRT 字幕文件

**请求体**:

```json
{
  "mediaUrl": "https://example.com/audio.mp3"
}
```

**响应**:

```json
{
  "taskId": "transcribe_1703123456789"
}
```

### 2. 查询转录状态

**端点**: `GET /api/transcribe/:taskId/status`

**描述**: 查询转录任务状态

**响应**:

```json
{
  "taskId": "transcribe_1703123456789",
  "status": "completed",
  "progress": 100,
  "result": {
    "subtitles": [
      {
        "start": "00:00:00,000",
        "end": "00:00:03,500",
        "text": "Hello, this is a test."
      }
    ],
    "language": "en-US",
    "confidence": 0.95
  }
}
```

### 3. 下载字幕

**端点**: `GET /api/transcribe/:taskId/download`

**描述**: 下载生成的 SRT 字幕文件

**响应**:

- 成功: 返回 SRT 文件内容
- 失败: JSON 错误信息

## 数据类型定义

### CanvasElement

```typescript
interface CanvasElement {
  type: "video" | "image" | "audio" | "text" | "shape";
  id: string;
  properties: ElementProperties;
  placement?: ElementPlacement;
  timeFrame: TimeFrame;
  opacity?: number;
  transition?: Transition;
  effects?: Effect[];
  border?: Border;
}
```

### ElementPlacement

```typescript
interface ElementPlacement {
  x: number; // X 坐标
  y: number; // Y 坐标
  width: number; // 宽度
  height: number; // 高度
  rotation?: number; // 旋转角度（度）
  scaleX: number; // X 轴缩放
  scaleY: number; // Y 轴缩放
  flipX?: boolean; // X 轴翻转
  flipY?: boolean; // Y 轴翻转
  cropX?: number; // 裁剪 X 坐标
  cropY?: number; // 裁剪 Y 坐标
  cropWidth?: number; // 裁剪宽度
  cropHeight?: number; // 裁剪高度
}
```

### TimeFrame

```typescript
interface TimeFrame {
  start: number; // 开始时间（毫秒）
  end: number; // 结束时间（毫秒）
}
```

### Transition

```typescript
interface Transition {
  in?: string; // 入场动画类型
  out?: string; // 出场动画类型
  duration?: number; // 动画持续时间（秒）
  inDuration?: number; // 入场动画持续时间
  outDuration?: number; // 出场动画持续时间
}
```

### Effect

```typescript
interface Effect {
  type: string; // 效果类型
  params: any; // 效果参数
}
```

### Border

```typescript
interface Border {
  color: string; // 边框颜色
  width: number; // 边框宽度
  style: string; // 边框样式
}
```

## 错误代码

| 错误代码              | 描述           | HTTP 状态码 |
| --------------------- | -------------- | ----------- |
| `INVALID_REQUEST`     | 请求参数无效   | 400         |
| `TASK_NOT_FOUND`      | 任务不存在     | 404         |
| `PROCESSING_ERROR`    | 处理过程中出错 | 500         |
| `TIMEOUT_ERROR`       | 处理超时       | 408         |
| `QUEUE_FULL`          | 任务队列已满   | 503         |
| `RATE_LIMIT_EXCEEDED` | 超出速率限制   | 429         |

## 使用示例

### JavaScript/TypeScript

```javascript
// 生成视频
async function generateVideo(canvasState) {
  try {
    const response = await fetch("/api/generateVideo", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(canvasState),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return result.taskId;
  } catch (error) {
    console.error("生成视频失败:", error);
    throw error;
  }
}

// 轮询进度
async function pollProgress(taskId) {
  const poll = async () => {
    try {
      const response = await fetch(`/api/progress/${taskId}`);
      const progress = await response.json();

      if (progress.status === "completed") {
        console.log("视频生成完成!");
        return progress;
      } else if (progress.status === "failed") {
        throw new Error(progress.error);
      } else {
        console.log(`进度: ${progress.progress}%`);
        setTimeout(poll, 1000); // 1秒后再次查询
      }
    } catch (error) {
      console.error("查询进度失败:", error);
    }
  };

  poll();
}

// 下载视频
function downloadVideo(taskId) {
  const link = document.createElement("a");
  link.href = `/api/download/${taskId}`;
  link.download = `video-${taskId}.mp4`;
  link.click();
}
```

### cURL 示例

```bash
# 生成视频
curl -X POST http://localhost:8080/api/generateVideo \
  -H "Content-Type: application/json" \
  -d @canvas-state.json

# 查询进度
curl http://localhost:8080/api/progress/1703123456789

# 下载视频
curl -O http://localhost:8080/api/download/1703123456789
```

## 性能优化建议

1. **批量处理**: 尽量减少 API 调用频率
2. **进度查询**: 建议每 1-2 秒查询一次进度，避免过于频繁
3. **错误重试**: 实现指数退避重试机制
4. **缓存**: 对于相同的画布状态，可以缓存结果
5. **压缩**: 使用 gzip 压缩大型请求体

## 限制和约束

- **最大视频时长**: 3600 秒（1 小时）
- **最大分辨率**: 7680x4320 (8K)
- **最大文件大小**: 50MB（请求体）
- **并发任务数**: 最多 10 个并发任务
- **任务保留时间**: 完成的任务保留 24 小时

## 第三方 API 集成

### Jamendo 音乐 API

**基础 URL**: `https://api.jamendo.com/v3.0`

#### 获取热门音乐

```javascript
const getPopularTracks = async (limit = 20, offset = 0) => {
  const response = await axios.get(`${BASE_URL}/tracks/`, {
    params: {
      client_id: API_KEY,
      format: "json",
      limit,
      offset,
      boost: "popularity_total",
      include: "musicinfo",
      audioformat: "mp32",
    },
  });

  return response.data.results;
};
```

#### 搜索音乐

```javascript
const searchTracks = async (query, limit = 20, offset = 0) => {
  const response = await axios.get(`${BASE_URL}/tracks/`, {
    params: {
      client_id: API_KEY,
      format: "json",
      limit,
      offset,
      namesearch: query,
      include: "musicinfo",
      audioformat: "mp32",
    },
  });

  return response.data.results;
};
```

### Pexels 图片/视频 API

**基础 URL**: `https://api.pexels.com/v1`

#### 获取热门图片

```javascript
const getPopularImages = async (page = 1, perPage = 20) => {
  const response = await axios.get(`${BASE_URL}/curated`, {
    headers: {
      Authorization: PEXELS_API_KEY,
    },
    params: { page, per_page: perPage },
  });

  return response.data.photos;
};
```

#### 搜索视频

```javascript
const searchVideos = async (query, page = 1, perPage = 20) => {
  const response = await axios.get(`${BASE_URL}/videos/search`, {
    headers: {
      Authorization: PEXELS_API_KEY,
    },
    params: { query, page, per_page: perPage },
  });

  return response.data.videos;
};
```

## WebSocket 实时通信

### 连接建立

```javascript
const socket = new WebSocket("ws://localhost:8080/ws");

socket.onopen = () => {
  console.log("WebSocket 连接已建立");
};

socket.onmessage = (event) => {
  const data = JSON.parse(event.data);
  handleRealtimeUpdate(data);
};
```

### 实时进度更新

```javascript
// 订阅任务进度
socket.send(
  JSON.stringify({
    type: "subscribe",
    taskId: "your-task-id",
  })
);

// 接收进度更新
const handleRealtimeUpdate = (data) => {
  if (data.type === "progress") {
    updateProgressBar(data.progress);
  } else if (data.type === "completed") {
    showCompletionNotification(data.result);
  }
};
```

## 高级功能 API

### 批量处理

```javascript
// 批量生成多个视频
const batchGenerate = async (canvasStates) => {
  const response = await fetch("/api/batch/generateVideos", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({
      batch: canvasStates,
      options: {
        parallel: true,
        maxConcurrent: 3,
      },
    }),
  });

  return response.json();
};
```

### 模板管理

```javascript
// 保存模板
const saveTemplate = async (template) => {
  const response = await fetch("/api/templates", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(template),
  });

  return response.json();
};

// 获取模板列表
const getTemplates = async (category = "all") => {
  const response = await fetch(`/api/templates?category=${category}`);
  return response.json();
};
```

## 错误处理最佳实践

### 统一错误处理

```javascript
class APIClient {
  async request(url, options = {}) {
    try {
      const response = await fetch(url, {
        ...options,
        headers: {
          "Content-Type": "application/json",
          ...options.headers,
        },
      });

      if (!response.ok) {
        const error = await response.json();
        throw new APIError(error.message, response.status, error.code);
      }

      return response.json();
    } catch (error) {
      if (error instanceof APIError) {
        throw error;
      }
      throw new APIError("网络请求失败", 0, "NETWORK_ERROR");
    }
  }
}

class APIError extends Error {
  constructor(message, status, code) {
    super(message);
    this.status = status;
    this.code = code;
    this.name = "APIError";
  }
}
```

### 重试机制

```javascript
const retryRequest = async (fn, maxRetries = 3, delay = 1000) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      if (i === maxRetries - 1) throw error;

      // 指数退避
      await new Promise((resolve) =>
        setTimeout(resolve, delay * Math.pow(2, i))
      );
    }
  }
};

// 使用示例
const generateVideo = (data) =>
  retryRequest(
    () =>
      apiClient.request("/api/generateVideo", {
        method: "POST",
        body: JSON.stringify(data),
      }),
    3,
    1000
  );
```

## 版本信息

- **当前版本**: v1.0.0
- **API 版本**: v1
- **最后更新**: 2024-01-01

## 更新日志

### v1.0.0 (2024-01-01)

- 初始版本发布
- 基础视频生成功能
- 转录服务集成
- 第三方 API 集成

### 即将发布的功能

- WebSocket 实时通信
- 批量处理 API
- 模板管理系统
- 高级动画效果
